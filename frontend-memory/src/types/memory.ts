export interface Message {
  role: string;
  content: string;
}

export interface Memory {
  id: string;
  memory: string;
  user_id: string;
  created_at: string;
  updated_at: string;
  metadata?: Record<string, any>;
}

export interface MemoryInput {
  messages: Message[];
  user_id: string;
  metadata?: Record<string, any>;
  infer?: boolean;
}

export interface MemorySearchInput {
  query: string;
  user_id: string;
  limit?: number;
}

export interface MemoryResponse {
  id: string;
  success: boolean;
  message: string;
}

export interface MemorySearchResponse {
  memories: Memory[];
  count: number;
}
