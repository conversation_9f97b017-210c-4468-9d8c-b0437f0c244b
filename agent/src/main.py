"""
Agent Memory Service

This service provides memory management for the agent system using mem0.
It handles storing, retrieving, and analyzing user interactions.
"""

import os
import logging
from typing import Dict, List, Optional, Any, Union
from datetime import datetime
from uuid import UUID

from fastapi import FastAPI, HTTPException, Depends, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
from dotenv import load_dotenv

# Import mem0 components
from mem0 import Memory

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

# Initialize FastAPI app
app = FastAPI(
    title="Agent Memory Service",
    description="Memory management for the agent system using mem0",
    version="0.1.0",
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Update this in production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize mem0 Memory
try:
    # Check if we should use local or remote configuration
    if os.getenv("MEM0_USE_LOCAL", "true").lower() == "true":
        # Local configuration with Supabase
        # Choose embedding model: nomic-embed-text (768 dims) or snowflake-arctic-embed2 (1024 dims)
        embedding_model = os.getenv("EMBEDDER_MODEL", "nomic-embed-text:latest")
        embedding_dims = 1024 if embedding_model == "snowflake-arctic-embed2" else 768

        # 修改connection_string来指定vecs schema
        connection_string = os.getenv("SUPABASE_DB_STRING", "postgresql://postgres:postgres@localhost:54322/postgres")
        # 添加search_path参数来指定schema
        if "?" in connection_string:
            connection_string += "&options=-c search_path=vecs,public"
        else:
            connection_string += "?options=-c search_path=vecs,public"

        # Configure LLM
        llm_provider = os.getenv("LLM_PROVIDER", "ollama")
        llm_config = {
            "model": os.getenv("LLM_MODEL", "qwen3:4b"),
            "temperature": 0,
            "max_tokens": 2000,
        }

        if llm_provider == "openai":
            llm_config.update({
                "api_key": os.getenv("OPENAI_API_KEY"),
            })
            # Note: OpenAI LLM doesn't support custom base_url in mem0
        else:
            llm_config["ollama_base_url"] = os.getenv("OLLAMA_BASE_URL", "http://localhost:11434")

        # Configure Embedder
        embedder_provider = os.getenv("EMBEDDER_PROVIDER", "ollama")
        embedder_config = {
            "model": embedding_model,
        }

        if embedder_provider == "openai":
            embedder_config.update({
                "api_key": os.getenv("OPENAI_API_KEY"),
            })
            # Note: OpenAI embedder doesn't support custom base_url in mem0
        else:
            embedder_config["ollama_base_url"] = os.getenv("OLLAMA_BASE_URL", "http://localhost:11434")

        config = {
            "vector_store": {
                "provider": "supabase",
                "config": {
                    "collection_name": "memories",
                    "connection_string": connection_string,
                    "index_method": "hnsw",
                    "index_measure": "cosine_distance",
                    "embedding_model_dims": embedding_dims,
                }
            },
            "llm": {
                "provider": llm_provider,
                "config": llm_config
            },
            "embedder": {
                "provider": embedder_provider,
                "config": embedder_config
            },
        }
        memory = Memory.from_config(config)
    else:
        # Use mem0 platform with API key
        memory = Memory()

    logger.info("Memory service initialized successfully with Supabase vector store")
except Exception as e:
    logger.error(f"Failed to initialize memory service: {str(e)}")
    memory = None


# Pydantic models
class Message(BaseModel):
    role: str
    content: str


class MemoryInput(BaseModel):
    messages: List[Message]
    user_id: str
    metadata: Optional[Dict[str, Any]] = None
    infer: bool = False  # Disable inference to prevent unwanted memory merging


class MemorySearchInput(BaseModel):
    query: str
    user_id: str
    limit: Optional[int] = 5


class MemoryUpdateInput(BaseModel):
    memory_id: str
    data: str


class MemoryResponse(BaseModel):
    id: str
    success: bool
    message: str


class MemorySearchResponse(BaseModel):
    memories: List[Dict[str, Any]]
    count: int


# Dependency to check if memory service is available
async def get_memory():
    if memory is None:
        raise HTTPException(
            status_code=503,
            detail="Memory service is not available. Check server logs for details."
        )
    return memory


# Background task for memory analysis
async def analyze_memory(user_id: str):
    """
    Analyze user memories to generate insights.
    This runs as a background task.
    """
    try:
        logger.info(f"Starting memory analysis for user {user_id}")

        # Get memory client
        memory_client = get_memory()
        if not memory_client:
            logger.error("Memory client not available for analysis")
            return

        # Import analyzer here to avoid circular imports
        from .analyzer import MemoryAnalyzer

        # Create analyzer instance
        analyzer = MemoryAnalyzer(memory_client)

        # Perform analysis
        insights = await analyzer.analyze_user_memories(user_id)

        if insights:
            logger.info(f"Generated {len(insights)} insights for user {user_id}")
            # TODO: Store insights in database or cache for later retrieval
            # For now, just log the insights
            for insight in insights:
                logger.info(f"Insight for {user_id}: {insight['type']} - {insight['content']}")
        else:
            logger.info(f"No insights generated for user {user_id}")

        logger.info(f"Completed memory analysis for user {user_id}")
    except Exception as e:
        logger.error(f"Error in memory analysis for user {user_id}: {str(e)}")


# API endpoints
@app.get("/")
async def root():
    """Health check endpoint with model information"""
    # Get model configuration from environment variables
    llm_provider = os.getenv("LLM_PROVIDER", "ollama")
    llm_model = os.getenv("LLM_MODEL", "qwen3:4b")
    embedder_provider = os.getenv("EMBEDDER_PROVIDER", "ollama")
    embedder_model = os.getenv("EMBEDDER_MODEL", "nomic-embed-text:latest")

    # Determine embedding dimensions
    embedding_dims = 1024 if embedder_model == "snowflake-arctic-embed2" else 768

    # Get base URLs based on provider
    llm_base_url = os.getenv("OPENAI_BASE_URL") if llm_provider == "openai" else os.getenv("OLLAMA_BASE_URL", "http://localhost:11434")
    embedder_base_url = os.getenv("OPENAI_BASE_URL") if embedder_provider == "openai" else os.getenv("OLLAMA_BASE_URL", "http://localhost:11434")

    return {
        "status": "ok",
        "service": "Agent Memory",
        "version": "0.1.0",
        "models": {
            "llm": {
                "provider": llm_provider,
                "model": llm_model,
                "base_url": llm_base_url
            },
            "embedder": {
                "provider": embedder_provider,
                "model": embedder_model,
                "dimensions": embedding_dims,
                "base_url": embedder_base_url
            }
        },
        "vector_store": {
            "provider": "supabase",
            "collection": "memories",
            "index_method": "hnsw",
            "distance_metric": "cosine_distance"
        },
        "memory_service_available": memory is not None
    }


@app.get("/models")
async def get_models():
    """Get detailed information about the models being used"""
    llm_provider = os.getenv("LLM_PROVIDER", "ollama")
    llm_model = os.getenv("LLM_MODEL", "qwen3:4b")
    embedder_provider = os.getenv("EMBEDDER_PROVIDER", "ollama")
    embedder_model = os.getenv("EMBEDDER_MODEL", "nomic-embed-text:latest")

    # Determine embedding dimensions
    embedding_dims = 1024 if embedder_model == "snowflake-arctic-embed2" else 768

    # Get base URLs based on provider
    llm_base_url = os.getenv("OPENAI_BASE_URL") if llm_provider == "openai" else os.getenv("OLLAMA_BASE_URL", "http://localhost:11434")
    embedder_base_url = os.getenv("OPENAI_BASE_URL") if embedder_provider == "openai" else os.getenv("OLLAMA_BASE_URL", "http://localhost:11434")

    return {
        "inference_model": {
            "provider": llm_provider,
            "model": llm_model,
            "base_url": llm_base_url,
            "temperature": 0,
            "max_tokens": 2000,
            "description": "Large Language Model used for reasoning and inference"
        },
        "embedding_model": {
            "provider": embedder_provider,
            "model": embedder_model,
            "dimensions": embedding_dims,
            "base_url": embedder_base_url,
            "description": "Embedding model used for vector representations"
        },
        "vector_store": {
            "provider": "supabase",
            "collection": "memories",
            "index_method": "hnsw",
            "distance_metric": "cosine_distance",
            "description": "Vector database for storing and searching embeddings"
        },
        "configuration": {
            "mem0_use_local": os.getenv("MEM0_USE_LOCAL", "true"),
            "analysis_enabled": os.getenv("ANALYSIS_ENABLED", "true"),
            "analysis_interval_minutes": os.getenv("ANALYSIS_INTERVAL_MINUTES", "60")
        }
    }


@app.post("/memories", response_model=MemoryResponse)
async def add_memory(
    memory_input: MemoryInput,
    background_tasks: BackgroundTasks,
    mem: Memory = Depends(get_memory)
):
    """
    Add a new memory from user interaction
    """
    try:
        # Convert Pydantic messages to dict format expected by mem0
        messages = [msg.model_dump() for msg in memory_input.messages]

        # Add memory using mem0
        result = mem.add(
            messages,
            user_id=memory_input.user_id,
            metadata=memory_input.metadata or {},
            infer=memory_input.infer
        )

        # Schedule background analysis
        background_tasks.add_task(analyze_memory, memory_input.user_id)

        return {
            "id": result.get("id", "unknown"),
            "success": True,
            "message": "Memory added successfully"
        }
    except Exception as e:
        logger.error(f"Error adding memory: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to add memory: {str(e)}")


@app.get("/memories/{user_id}", response_model=MemorySearchResponse)
async def get_all_memories(
    user_id: str,
    mem: Memory = Depends(get_memory)
):
    """
    Get all memories for a user - using direct database query for speed
    """
    try:
        logger.info(f"Getting all memories for user: {user_id}")

        # Use direct database query for better performance
        # Skip mem0's get_all() method due to known bugs and performance issues
        try:
            import psycopg2

            connection_string = os.getenv("SUPABASE_DB_STRING", "postgresql://postgres:postgres@localhost:54322/postgres")
            conn = psycopg2.connect(connection_string)
            cur = conn.cursor()

            # Query vecs.memories table directly
            # The memory content is in metadata->>'data'
            # The timestamps are in metadata->>'created_at' and metadata->>'updated_at'
            cur.execute("""
                SELECT id,
                       metadata->>'data' as memory,
                       metadata,
                       COALESCE(metadata->>'created_at', NOW()::text) as created_at,
                       COALESCE(metadata->>'updated_at', metadata->>'created_at', NOW()::text) as updated_at
                FROM vecs.memories
                WHERE metadata->>'user_id' = %s
                ORDER BY COALESCE(metadata->>'created_at', NOW()::text) DESC
            """, (user_id,))

            rows = cur.fetchall()
            logger.info(f"Direct DB query found {len(rows)} memories for user {user_id}")

            memories_list = []
            for row in rows:
                memory_dict = {
                    "id": row[0],
                    "memory": row[1],
                    "metadata": row[2] if row[2] else {},
                    "created_at": row[3] if row[3] else None,
                    "updated_at": row[4] if row[4] else None,
                }
                memories_list.append(memory_dict)

            conn.close()

            return {
                "memories": memories_list,
                "count": len(memories_list)
            }

        except Exception as db_error:
            logger.error(f"Direct database query failed: {db_error}")
            logger.info("Falling back to mem0 get_all() method...")

            # Fallback to mem0 method if direct query fails
            try:
                memories = mem.get_all(user_id=user_id)
                logger.info(f"mem0 memories response type: {type(memories)}")

                # Process mem0 response
                if isinstance(memories, list):
                    memories_list = memories
                elif isinstance(memories, dict):
                    if 'results' in memories:
                        memories_list = memories['results']
                    elif 'memories' in memories:
                        memories_list = memories['memories']
                    else:
                        memories_list = [memories] if memories else []
                else:
                    logger.warning(f"Unexpected memories type: {type(memories)}")
                    memories_list = []

                return {
                    "memories": memories_list,
                    "count": len(memories_list)
                }

            except AttributeError as attr_error:
                if "'list' object has no attribute 'id'" in str(attr_error):
                    logger.warning(f"Known mem0 bug encountered: {attr_error}")
                    return {
                        "memories": [],
                        "count": 0
                    }
                else:
                    raise attr_error

    except Exception as e:
        logger.error(f"Error retrieving memories: {str(e)}")
        logger.error(f"Exception type: {type(e)}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"Failed to retrieve memories: {str(e)}")


@app.post("/memories/search", response_model=MemorySearchResponse)
async def search_memories(
    search_input: MemorySearchInput,
    mem: Memory = Depends(get_memory)
):
    """
    Search for relevant memories
    """
    try:
        logger.info(f"Searching memories for user: {search_input.user_id}, query: {search_input.query}")
        memories = mem.search(
            query=search_input.query,
            user_id=search_input.user_id,
            limit=search_input.limit
        )
        logger.info(f"Search memories response type: {type(memories)}")
        logger.info(f"Search memories response: {memories}")

        # Handle the case where memories might be a list or dict with results
        if isinstance(memories, list):
            memories_list = memories
        elif isinstance(memories, dict):
            # Handle case where it might return a dict with results
            if 'results' in memories:
                memories_list = memories['results']
            elif 'memories' in memories:
                memories_list = memories['memories']
            else:
                # If it's a single memory dict, wrap it in a list
                memories_list = [memories] if memories else []
        else:
            logger.warning(f"Unexpected search memories type: {type(memories)}, value: {memories}")
            memories_list = []

        logger.info(f"Processed search memories count: {len(memories_list)}")

        return {
            "memories": memories_list,
            "count": len(memories_list)
        }
    except Exception as e:
        logger.error(f"Error searching memories: {str(e)}")
        logger.error(f"Exception type: {type(e)}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"Failed to search memories: {str(e)}")


@app.put("/memories", response_model=MemoryResponse)
async def update_memory(
    update_input: MemoryUpdateInput,
    mem: Memory = Depends(get_memory)
):
    """
    Update an existing memory
    """
    try:
        result = mem.update(
            memory_id=update_input.memory_id,
            data=update_input.data
        )
        # Handle different return types from mem0
        memory_id = update_input.memory_id
        if isinstance(result, dict) and 'id' in result:
            memory_id = result['id']
        elif isinstance(result, list) and len(result) > 0 and isinstance(result[0], dict) and 'id' in result[0]:
            memory_id = result[0]['id']

        return {
            "id": memory_id,
            "success": True,
            "message": "Memory updated successfully"
        }
    except Exception as e:
        logger.error(f"Error updating memory: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to update memory: {str(e)}")


@app.delete("/memories/{memory_id}", response_model=MemoryResponse)
async def delete_memory(
    memory_id: str,
    mem: Memory = Depends(get_memory)
):
    """
    Delete a specific memory
    """
    try:
        result = mem.delete(memory_id=memory_id)
        # Handle different return types from mem0
        return {
            "id": memory_id,
            "success": True,
            "message": "Memory deleted successfully"
        }
    except Exception as e:
        logger.error(f"Error deleting memory: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to delete memory: {str(e)}")


@app.delete("/memories/user/{user_id}", response_model=MemoryResponse)
async def delete_all_user_memories(
    user_id: str,
    mem: Memory = Depends(get_memory)
):
    """
    Delete all memories for a user
    """
    try:
        mem.delete_all(user_id=user_id)
        return {
            "id": user_id,
            "success": True,
            "message": f"All memories for user {user_id} deleted successfully"
        }
    except Exception as e:
        logger.error(f"Error deleting user memories: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to delete user memories: {str(e)}")


if __name__ == "__main__":
    import uvicorn
    uvicorn.run("main:app", host="0.0.0.0", port=8011, reload=True)
